import Store from '@/store/';
import { getHomePageByRole } from '@/utils/home';

export default [
  {
    path: '/login',
    name: '登录页',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/login.vue') : import('@/page/login/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/oauth/redirect/:source',
    name: '第三方登录',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/login.vue') : import('@/page/login/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/lock',
    name: '锁屏页',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/lock.vue') : import('@/page/lock/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/404.vue'),
    name: '404',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/403',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/403.vue'),
    name: '403',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/500',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/500.vue'),
    name: '500',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/',
    name: '首页',
    redirect: () => {
      // 动态重定向到基于角色的首页
      const userInfo = Store.getters.userInfo;
      return getHomePageByRole(userInfo);
    },
  },
{
  path: '/project-assistant-waterfall',
  name: '项目编制助手(瀑布流)',
  meta: {
    keepAlive: true,
    isTab: false,
    isAuth: false,
  },
  component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/projectAssistantWaterfall.vue')
},
{
  path: '/user_home',
  name: '用户端首页',
  meta: {
    keepAlive: true,
    isTab: false,
    isAuth: false,
  },
  component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/user_home.vue'),
},

];
