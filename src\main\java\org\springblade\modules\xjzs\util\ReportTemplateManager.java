package org.springblade.modules.xjzs.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.xjzs.entity.ProjectReportEntity;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 报告模板管理器
 * 用于管理不同类型的报告模板生成
 */
@Slf4j
public class ReportTemplateManager {

    /**
     * 报告类型枚举
     */
    public enum ReportType {
        TRAINING("培训类", "doctemplete/peixun.docx"),
        ENGINEERING("工程类", "doctemplete/gongcheng.docx"),
        PROCUREMENT("采购类", "doctemplete/caigou.docx");

        private final String displayName;
        private final String templatePath;

        ReportType(String displayName, String templatePath) {
            this.displayName = displayName;
            this.templatePath = templatePath;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getTemplatePath() {
            return templatePath;
        }

        /**
         * 根据算法类别获取报告类型
         */
        public static ReportType fromAlgorithmCategory(String algorithmCategory) {
            if ("工程咨询类".equals(algorithmCategory)) {
                return ENGINEERING;
            } else if ("培训类".equals(algorithmCategory)) {
                return TRAINING;
            } else {
                return PROCUREMENT; // 默认为采购类
            }
        }
    }

    /**
     * 生成报告文档
     */
    public static XWPFDocument generateReport(ProjectReportEntity report, String tableItems, String tabledata) {
        // 根据算法类别确定报告类型
        ReportType reportType = ReportType.fromAlgorithmCategory(report.getAlgorithmCategory());

        try {
            // 尝试加载模板
            XWPFDocument document = WordTemplateUtil.loadTemplate(reportType.getTemplatePath());

            // 准备占位符数据
            Map<String, String> placeholders = preparePlaceholders(report, tableItems, tabledata, reportType);

            // 替换占位符
            WordTemplateUtil.replacePlaceholders(document, placeholders);

            // 如果是工程类，在替换占位符后插入真正的表格
            if (reportType == ReportType.ENGINEERING) {
                insertEngineeringTable(document, tableItems, tabledata);
            }

            return document;
        } catch (IOException e) {
            log.error("加载{}模板失败: {}", reportType.getDisplayName(), e.getMessage());
            throw new RuntimeException("模板文件不存在或无法加载: " + reportType.getTemplatePath(), e);
        }
    }

    /**
     * 准备占位符数据
     */
    private static Map<String, String> preparePlaceholders(ProjectReportEntity report, String tableItems,
                                                          String tabledata, ReportType reportType) {
        Map<String, String> placeholders = new HashMap<>();

        // 对于工程类，只需要两个占位符：projectName 和 projectContent
        if (reportType == ReportType.ENGINEERING) {
            // 项目名称
            if (report.getProject() != null) {
                placeholders.put("projectName", report.getProject().getName());
            }

            // 项目内容（表格占位符）
            String projectContent = buildProjectContent(tableItems, tabledata, reportType);
            placeholders.put("projectContent", projectContent);

            return placeholders;
        }

        // 对于其他类型（培训类、采购类），保留原有的完整占位符逻辑
        // 基本信息
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        String currentDate = dateFormat.format(new Date());
        String currentUser = AuthUtil.getNickName();

        placeholders.put("reportDate", currentDate);
        placeholders.put("reportAuthor", currentUser);
        placeholders.put("reportId", report.getReportId());

        // 项目信息
        if (report.getProject() != null) {
            placeholders.put("projectName", report.getProject().getName());
            placeholders.put("projectType", report.getProject().getType());
            placeholders.put("projectBudget", report.getProject().getBudget() != null ?
                    report.getProject().getBudget().toString() : "");
            placeholders.put("procurementMethod", report.getProject().getProcurementMethod());

            // 根据报告类型生成不同的内容
            String projectContent = buildProjectContent(tableItems, tabledata, reportType);
            placeholders.put("projectContent", projectContent);
        }

        // 不需要设置特定内容，都使用模板自带内容

        // 最高限价
        BigDecimal totalPrice = report.getTotalPrice();
        if (totalPrice != null) {
            placeholders.put("totalPrice", String.format("%.2f", totalPrice));
            placeholders.put("unitPrice", String.format("%.2f", totalPrice));
            placeholders.put("discount", "0.95");
            placeholders.put("preferential", "5%");
        }

        return placeholders;
    }



    /**
     * 根据报告类型构建项目内容
     */
    private static String buildProjectContent(String tableItems, String tabledata, ReportType reportType) {
        if (tabledata == null || tabledata.isEmpty()) {
            return "";
        }
        
        switch (reportType) {
            case TRAINING:
                return buildTrainingContent(tableItems, tabledata);
            case ENGINEERING:
                return buildEngineeringContent(tableItems, tabledata);
            default:
                return buildProcurementContent(tableItems, tabledata);
        }
    }

    /**
     * 构建培训类内容
     */
    private static String buildTrainingContent(String tableItems, String tabledata) {
        JSONArray tabledataArray = new JSONArray(tabledata);
        StringBuilder resultTable = new StringBuilder();
        
        // 添加空值检查
        if(tabledataArray.length() > 0 && tabledataArray.getJSONObject(0).has("trainingLocation")) {
            resultTable.append("培训方式:").
                    append(tabledataArray.getJSONObject(0).getString("trainingLocation"))
                    .append(",人数:").append(tabledataArray.getJSONObject(0).getInt("trainingPeople"))
                    .append("人,每日学时:").append(tabledataArray.getJSONObject(0).getInt("hoursPerDay"))
                    .append("小时,培训天数:").append(tabledataArray.getJSONObject(0).getInt("trainingDays"))
                    .append("天。\n");
        }

        JSONArray jsonArray = new JSONArray(tableItems);
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String name = jsonObject.getString("name");
            Integer unitPrice = jsonObject.getInt("unitPrice");
            String unit = jsonObject.getString("unit");
            Integer quantity = jsonObject.getInt("quantity");
            Integer totalPrice = jsonObject.getInt("totalPrice");

            result.append("\n").append(name).append("，标准:").append(unitPrice).append("元/").append(unit).append("，共：").append(quantity).append(unit).append("，金额:").append(totalPrice).append("元；\n");
        }

        // Calculate total
        int totalSum = 0;
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            totalSum += jsonObject.getInt("totalPrice");
        }

        // Final result string
        return resultTable.toString() + result.append("\n合计：").append(totalSum).append("元").toString();
    }

    /**
     * 构建工程类内容
     */
    private static String buildEngineeringContent(String tableItems, String tabledata) {
        // 返回一个特殊标记，稍后会被真正的表格替换，不包含"详见下表："文字
        return "[ENGINEERING_TABLE_PLACEHOLDER]";
    }

    /**
     * 在Word文档中插入工程类表格
     */
    private static void insertEngineeringTable(XWPFDocument document, String tableItems, String tabledata) {
        if (tableItems == null || tableItems.isEmpty()) {
            return;
        }

        try {
            JSONArray jsonArray = new JSONArray(tableItems);
            if (jsonArray.length() == 0) {
                return;
            }

            // 查找包含占位符的段落，并在其后插入表格
            XWPFParagraph targetParagraph = null;
            int targetParagraphIndex = -1;

            for (int i = 0; i < document.getParagraphs().size(); i++) {
                XWPFParagraph paragraph = document.getParagraphs().get(i);
                String text = paragraph.getText();
                if (text != null && text.contains("[ENGINEERING_TABLE_PLACEHOLDER]")) {
                    targetParagraph = paragraph;
                    targetParagraphIndex = i;
                    // 清除占位符，不添加任何文字
                    // 不能直接clear()，需要逐个移除runs
                    while (paragraph.getRuns().size() > 0) {
                        paragraph.removeRun(0);
                    }
                    // 不添加任何文字，让表格直接出现在这个位置
                    break;
                }
            }

            // 在指定位置插入表格
            XWPFTable table;
            if (targetParagraphIndex != -1) {
                // 使用 insertNewTbl 方法在目标段落后插入表格
                try {
                    // 获取目标段落的 CTP (底层XML对象)，然后创建 XmlCursor
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP ctp = targetParagraph.getCTP();
                    org.apache.xmlbeans.XmlCursor cursor = ctp.newCursor();

                    // 移动光标到段落后面
                    cursor.toEndToken();
                    cursor.toNextToken();

                    // 在光标位置插入新表格
                    table = document.insertNewTbl(cursor);

                    // 关闭光标
                    cursor.dispose();

                    log.info("成功在第四点位置插入工程类表格");
                } catch (Exception e) {
                    log.warn("无法在指定位置插入表格，将在文档末尾创建: {}", e.getMessage());
                    table = document.createTable();
                }
            } else {
                // 如果找不到占位符，在文档末尾创建表格
                table = document.createTable();
            }

            // 创建表头
            XWPFTableRow headerRow = table.getRow(0);
            headerRow.getCell(0).setText("序号");
            headerRow.addNewTableCell().setText("项目名称");
            headerRow.addNewTableCell().setText("规格型号");
            headerRow.addNewTableCell().setText("单位");
            headerRow.addNewTableCell().setText("数量");
            headerRow.addNewTableCell().setText("单价(元)");
            headerRow.addNewTableCell().setText("金额(元)");

            // 不设置任何样式，保持文档原有格式

            // 添加数据行
            int totalSum = 0;
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String name = jsonObject.optString("name", "");
                String specifications = jsonObject.optString("specifications", "工程造价咨询服务");
                String unit = jsonObject.optString("unit", "项");
                int quantity = jsonObject.optInt("quantity", 1);
                int unitPrice = jsonObject.optInt("unitPrice", 0);
                int totalPrice = jsonObject.optInt("totalPrice", unitPrice * quantity);

                XWPFTableRow dataRow = table.createRow();
                dataRow.getCell(0).setText(String.valueOf(i + 1));
                dataRow.getCell(1).setText(name);
                dataRow.getCell(2).setText(specifications);
                dataRow.getCell(3).setText(unit);
                dataRow.getCell(4).setText(String.valueOf(quantity));
                dataRow.getCell(5).setText(String.format("%.2f", (double) unitPrice));
                dataRow.getCell(6).setText(String.format("%.2f", (double) totalPrice));

                totalSum += totalPrice;
            }

            // 添加合计行
            XWPFTableRow totalRow = table.createRow();

            // 合并前6个单元格并设置"合计"文本
            totalRow.getCell(0).setText("合计");

            // 清空其他单元格的文本，但保留单元格结构
            for (int i = 1; i < 6; i++) {
                totalRow.getCell(i).setText("");
            }

            // 设置最后一列的总金额
            totalRow.getCell(6).setText(String.format("%.2f", (double) totalSum));

            // 合并单元格 - 将前6个单元格合并
            mergeCellsHorizontally(totalRow, 0, 5);

            // 设置合计行样式 - 只给有内容的单元格设置粗体
            // 第一个单元格（合计文字）
            XWPFParagraph firstCellParagraph = totalRow.getCell(0).getParagraphArray(0);
            if (firstCellParagraph.getRuns().size() > 0) {
                firstCellParagraph.getRuns().get(0).setBold(true);
            }

            // 最后一个单元格（总金额）
            XWPFParagraph lastCellParagraph = totalRow.getCell(6).getParagraphArray(0);
            if (lastCellParagraph.getRuns().size() > 0) {
                lastCellParagraph.getRuns().get(0).setBold(true);
            }

            // 设置表格样式
            table.setWidth("100%");

            // 设置表格边框（细线）
            setTableBorders(table);

            log.info("成功创建工程类表格，包含{}行数据，表格已插入到第四点位置", jsonArray.length());

        } catch (Exception e) {
            log.error("插入工程类表格失败", e);
        }
    }

    /**
     * 设置表格统一边框样式
     */
    private static void setTableBorders(XWPFTable table) {
        try {
            // 获取表格属性
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr tblPr = table.getCTTbl().getTblPr();
            if (tblPr == null) {
                tblPr = table.getCTTbl().addNewTblPr();
            }

            // 获取或创建表格边框
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblBorders borders = tblPr.getTblBorders();
            if (borders == null) {
                borders = tblPr.addNewTblBorders();
            }

            // 设置统一的边框样式
            org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.Enum borderStyle =
                org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE;
            BigInteger borderSize = BigInteger.valueOf(2); // 2磅细线
            String borderColor = "000000"; // 黑色

            // 上边框
            if (borders.getTop() == null) {
                borders.addNewTop();
            }
            borders.getTop().setVal(borderStyle);
            borders.getTop().setSz(borderSize);
            borders.getTop().setSpace(BigInteger.valueOf(0));
            borders.getTop().setColor(borderColor);

            // 下边框
            if (borders.getBottom() == null) {
                borders.addNewBottom();
            }
            borders.getBottom().setVal(borderStyle);
            borders.getBottom().setSz(borderSize);
            borders.getBottom().setSpace(BigInteger.valueOf(0));
            borders.getBottom().setColor(borderColor);

            // 左边框
            if (borders.getLeft() == null) {
                borders.addNewLeft();
            }
            borders.getLeft().setVal(borderStyle);
            borders.getLeft().setSz(borderSize);
            borders.getLeft().setSpace(BigInteger.valueOf(0));
            borders.getLeft().setColor(borderColor);

            // 右边框
            if (borders.getRight() == null) {
                borders.addNewRight();
            }
            borders.getRight().setVal(borderStyle);
            borders.getRight().setSz(borderSize);
            borders.getRight().setSpace(BigInteger.valueOf(0));
            borders.getRight().setColor(borderColor);

            // 内部水平边框
            if (borders.getInsideH() == null) {
                borders.addNewInsideH();
            }
            borders.getInsideH().setVal(borderStyle);
            borders.getInsideH().setSz(borderSize);
            borders.getInsideH().setSpace(BigInteger.valueOf(0));
            borders.getInsideH().setColor(borderColor);

            // 内部垂直边框
            if (borders.getInsideV() == null) {
                borders.addNewInsideV();
            }
            borders.getInsideV().setVal(borderStyle);
            borders.getInsideV().setSz(borderSize);
            borders.getInsideV().setSpace(BigInteger.valueOf(0));
            borders.getInsideV().setColor(borderColor);

        } catch (Exception e) {
            log.error("设置表格统一边框失败", e);
        }
    }

    /**
     * 水平合并单元格
     */
    private static void mergeCellsHorizontally(XWPFTableRow row, int fromCol, int toCol) {
        try {
            // 设置第一个单元格的合并属性
            XWPFTableCell firstCell = row.getCell(fromCol);
            if (firstCell.getCTTc().getTcPr() == null) {
                firstCell.getCTTc().addNewTcPr();
            }

            // 设置水平合并开始
            firstCell.getCTTc().getTcPr().addNewHMerge().setVal(
                org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);

            // 设置其他单元格为合并继续
            for (int i = fromCol + 1; i <= toCol; i++) {
                XWPFTableCell cell = row.getCell(i);
                if (cell.getCTTc().getTcPr() == null) {
                    cell.getCTTc().addNewTcPr();
                }
                cell.getCTTc().getTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
            }

        } catch (Exception e) {
            log.error("合并单元格失败", e);
        }
    }

    /**
     * 构建采购类内容
     */
    private static String buildProcurementContent(String tableItems, String tabledata) {
        // 采购类内容构建逻辑，可以复用培训类的逻辑或自定义
        return buildTrainingContent(tableItems, tabledata);
    }



}
