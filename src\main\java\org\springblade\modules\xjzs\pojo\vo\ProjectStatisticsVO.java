/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.xjzs.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 首页统计数据VO
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@Schema(description = "首页统计数据")
public class ProjectStatisticsVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 未开始项目统计
     */
    @Schema(description = "未开始项目统计")
    private Integer notStartedProjectsCount;

    /**
     * 已完成项目统计
     */
    @Schema(description = "已完成项目统计")
    private Integer completedProjectsCount;

    /**
     * 智能帮助统计
     */
    @Schema(description = "智能帮助统计")
    private Integer companyUsageCount;

    /**
     * 公司项目金额（万元）
     */
    @Schema(description = "公司项目金额（万元）")
    private String companyAmount;

}
