import Layout from '@/page/index/index.vue';
import Store from '@/store/';

export default [
  {
    path: '/wel',
    component: () =>
    Store.getters.isMacOs ? import('@/mac/index.vue') : import('@/page/index/index.vue'),
    redirect: '/wel/index',
    children: [
      {
        path: 'index',
        name: '系统首页',
        meta: {
          i18n: 'dashboard',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/wel/index.vue'),
      },
      {
        path: 'dashboard',
        name: '控制台',
        meta: {
          i18n: 'dashboard',
          menu: false,
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/wel/dashboard.vue'),
      },
    ],
  },
  {
    path: '/xjzs',
    component: () => import('@/page/index/index.vue'),
    redirect: '/xjzs/manager_home',
    children: [
      {
        path: 'manager_home',
        name: '管理端首页',
        meta: {
          i18n: 'manager_home',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/manager_home.vue'),
      },
      {
        path: 'inquiry',
        name: '询价管理',
        meta: {
          i18n: 'inquiry',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/inquiry.vue'),
      },
      {
        path: 'price-calculator',
        name: '限价计算器',
        meta: {
          i18n: 'priceCalculator',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/priceCalculator.vue'),
      },
      {
        path: 'project-assistant',
        name: '项目编制助手',
        meta: {
          i18n: 'projectAssistant',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/projectAssistant.vue'),
      },
      {
        path: 'project-assistant-tabs',
        name: '项目编制助手(标签页)',
        meta: {
          i18n: 'projectAssistantTabs',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/projectAssistantTabs.vue'),
      },
      {
        path: 'file-library',
        name: '文件管理',
        meta: {
          i18n: 'fileLibrary',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/fileLibrary.vue'),
      }
    ],
  },
  {
    path: '/test',
    component: Layout,
    redirect: '/test/index',
    children: [
      {
        path: 'index',
        name: '测试页',
        meta: {
          i18n: 'test',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/util/test.vue'),
      },
    ],
  },
  {
    path: '/dict-horizontal',
    component: Layout,
    redirect: '/dict-horizontal/index',
    children: [
      {
        path: 'index',
        name: '字典管理',
        meta: {
          i18n: 'dict',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/util/demo/dict-horizontal.vue'),
      },
    ],
  },
  {
    path: '/dict-vertical',
    component: Layout,
    redirect: '/dict-vertical/index',
    children: [
      {
        path: 'index',
        name: '字典管理',
        meta: {
          i18n: 'dict',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/util/demo/dict-vertical.vue'),
      },
    ],
  },
  {
    path: '/info',
    component: Layout,
    redirect: '/info/index',
    children: [
      {
        path: 'index',
        name: '个人信息',
        meta: {
          i18n: 'info',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/system/userinfo.vue'),
      },
    ],
  },
  {
    path: '/work/process/leave',
    component: Layout,
    redirect: '/work/process/leave/form',
    children: [
      {
        path: 'form/:processDefinitionId',
        name: '请假流程',
        meta: {
          i18n: 'work',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/work/process/leave/form.vue'),
      },
      {
        path: 'handle/:taskId/:processInstanceId/:businessId',
        name: '处理请假流程',
        meta: {
          i18n: 'work',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/work/process/leave/handle.vue'),
      },
      {
        path: 'detail/:processInstanceId/:businessId',
        name: '请假流程详情',
        meta: {
          i18n: 'work',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/work/process/leave/detail.vue'),
      },
    ],
  },
  // 修改项目详情路由，指向projectAssistant组件
  {
    path: '/project-detail/:id',
    component: Layout,
    children: [
      {
        path: '',
        name: '项目详情',
        meta: {
          i18n: 'projectDetail',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/xjzs/projectAssistant.vue'),
      },
    ],
  },
];
