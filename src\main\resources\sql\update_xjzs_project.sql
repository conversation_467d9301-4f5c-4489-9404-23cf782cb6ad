-- 为xjzs_project表添加项目状态和经办人字段
-- 执行时间：2025-01-19

-- 添加项目状态字段
-- 添加项目状态字段
ALTER TABLE xjzs_project
ADD COLUMN project_status INTEGER DEFAULT 0,
ADD COLUMN handler_id BIGINT;

-- 添加字段注释（PostgreSQL 中 COMMENT 需要单独设置）
COMMENT ON COLUMN xjzs_project.project_status IS '项目状态：0-未开始，1-询价中，2-已完成，9-已终止';
COMMENT ON COLUMN xjzs_project.handler_id IS '经办人ID，关联blade_user表';

-- 添加索引以提高查询性能
CREATE INDEX idx_xjzs_project_handler_id ON xjzs_project(handler_id);
