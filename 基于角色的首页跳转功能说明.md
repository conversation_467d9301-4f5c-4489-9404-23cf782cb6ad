# 基于角色的首页跳转功能说明

## 功能概述

实现了基于用户角色的首页跳转功能，根据用户的角色名称自动跳转到相应的首页：

- **超级管理员** 或 **管理员组** → 跳转到 `/xjzs/manager_home` (管理端首页)
- **其他角色** → 跳转到 `/xjzs/user_home` (用户端首页)

## 实现的功能

### 1. 角色检测和首页路径获取

创建了 `src/utils/home.js` 工具文件，包含以下函数：

- `getHomePageByRole(userInfo)` - 根据用户角色获取首页路径
- `navigateToHomePage(router, userInfo)` - 跳转到用户对应的首页
- `isAdmin(userInfo)` - 检查当前用户是否为管理员
- `getUserRoleDisplayName(userInfo)` - 获取用户角色显示名称

### 2. 动态首页配置

修改了 `src/config/website.js`，添加了 `getHomePageByRole` 函数，支持动态获取首页路径。

### 3. 路由配置

#### 更新的路由路径：
- `/xjzs/manager_home` - 管理端首页
- `/xjzs/user_home` - 用户端首页
- `/xjzs/role-test` - 角色测试页面（用于调试）

#### 路由重定向：
- 根路径 `/` 现在会根据用户角色动态重定向
- `/xjzs` 默认重定向到 `/xjzs/user_home`

### 4. 登录流程集成

修改了以下文件以支持基于角色的跳转：

- `src/permission.js` - 路由守卫中的角色检测
- `src/page/login/userlogin.vue` - 登录成功后的跳转逻辑
- `src/router/page/index.js` - 根路径的动态重定向

## 使用方法

### 1. 角色配置

确保用户信息中包含正确的 `role_name` 字段，支持的管理员角色名称：
- `超级管理员`
- `管理员组`

### 2. 页面组件

- **管理端首页**: `src/views/xjzs/manager_home.vue`
- **用户端首页**: `src/views/xjzs/user_home.vue`

### 3. 编程式导航

```javascript
import { getHomePageByRole, navigateToHomePage, isAdmin } from '@/utils/home'

// 获取用户对应的首页路径
const homePage = getHomePageByRole(userInfo)

// 跳转到用户对应的首页
navigateToHomePage(this.$router, userInfo)

// 检查是否为管理员
const isAdminUser = isAdmin(userInfo)
```

## 测试功能

### 角色测试页面

访问 `/xjzs/role-test` 可以查看：
- 当前用户信息
- 角色检测结果
- 推荐的首页路径
- 调试信息

### 测试步骤

1. 使用不同角色的用户登录系统
2. 观察登录后的跳转页面
3. 访问角色测试页面验证角色检测逻辑
4. 测试手动跳转功能

## 配置说明

### 1. 修改角色判断逻辑

如需修改管理员角色的判断条件，编辑 `src/utils/home.js` 中的 `getHomePageByRole` 函数：

```javascript
export function getHomePageByRole(userInfo) {
  if (!userInfo || !userInfo.role_name) {
    return '/xjzs/user_home'; // 默认用户首页
  }
  
  const roleName = userInfo.role_name;
  
  // 在这里修改管理员角色的判断条件
  if (roleName === '超级管理员' || roleName === '管理员组' || roleName === '系统管理员') {
    return '/xjzs/manager_home';
  }
  
  return '/xjzs/user_home';
}
```

### 2. 添加新的角色类型

如需添加新的角色类型和对应的首页，可以扩展判断逻辑：

```javascript
export function getHomePageByRole(userInfo) {
  if (!userInfo || !userInfo.role_name) {
    return '/xjzs/user_home';
  }
  
  const roleName = userInfo.role_name;
  
  // 管理员角色
  if (roleName === '超级管理员' || roleName === '管理员组') {
    return '/xjzs/manager_home';
  }
  
  // 审核员角色
  if (roleName === '审核员') {
    return '/xjzs/auditor_home';
  }
  
  // 默认用户角色
  return '/xjzs/user_home';
}
```

### 3. 自定义默认首页

修改 `src/config/website.js` 中的 `fistPage` 配置：

```javascript
//首页配置
fistPage: {
  name: '首页',
  path: '/wel/index', // 保持原有配置作为后备
},
```

## 注意事项

1. **用户信息获取时机**: 确保在路由跳转时用户信息已经正确加载到store中
2. **角色名称匹配**: 角色名称必须完全匹配，区分大小写
3. **路由组件**: 确保对应的页面组件文件存在且可以正常加载
4. **权限控制**: 建议结合菜单权限系统，确保用户只能访问有权限的页面

## 故障排除

### 1. 跳转到错误页面

- 检查用户信息中的 `role_name` 字段是否正确
- 确认角色名称是否与代码中的判断条件匹配
- 查看浏览器控制台是否有错误信息

### 2. 无法跳转

- 确认路由配置是否正确
- 检查页面组件文件是否存在
- 验证用户是否已正确登录

### 3. 调试方法

- 访问 `/xjzs/role-test` 页面查看详细信息
- 在浏览器控制台查看用户信息和角色检测结果
- 检查Vue DevTools中的store状态

## 扩展建议

1. **缓存优化**: 可以考虑缓存角色检测结果，避免重复计算
2. **动态菜单**: 结合角色信息动态显示不同的菜单项
3. **权限路由**: 基于角色实现更细粒度的路由权限控制
4. **角色切换**: 支持用户在多个角色之间切换（如果适用）
