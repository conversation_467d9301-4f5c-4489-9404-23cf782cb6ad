# 项目状态和经办人功能实现说明

## 功能概述

本次更新为xjzs_project表添加了项目状态和经办人字段，实现了基于角色的权限控制和数据展示功能。

## 数据库更改

### 1. 新增字段

#### xjzs_project表新增字段：
- `status` INT DEFAULT 0 - 项目状态字段
  - 0：未开始
  - 1：询价中  
  - 2：已完成
  - 9：已终止

- `handler_id` BIGINT - 经办人字段，关联blade_user表的id字段

### 2. 索引优化
- `idx_xjzs_project_status` - 项目状态索引
- `idx_xjzs_project_handler_id` - 经办人索引  
- `idx_xjzs_project_create_time` - 创建时间索引

### 3. 执行脚本
```sql
-- 执行数据库更新脚本
source src/main/resources/sql/update_xjzs_project.sql

-- 插入测试数据（可选）
source src/main/resources/sql/test_data.sql
```

## 后端实现

### 1. 实体类更新

#### ProjectEntity.java
- 添加 `status` 字段（Integer类型）
- 添加 `handlerId` 字段（Long类型）

#### RecentProjectVO.java  
- 添加 `status` 字段（Integer类型）
- 添加 `statusName` 字段（String类型）
- 添加 `handlerId` 字段（Long类型）
- 添加 `handlerName` 字段（String类型）

### 2. 枚举类

#### ProjectStatusEnum.java
```java
public enum ProjectStatusEnum {
    NOT_STARTED(0, "未开始"),
    INQUIRING(1, "询价中"), 
    COMPLETED(2, "已完成"),
    TERMINATED(9, "已终止");
}
```

### 3. 服务层重构

#### HomeServiceImpl.java 主要更改：

**权限控制逻辑：**
```java
private boolean isAdminRole(User user) {
    String roleName = user.getRoleName();
    return "超级管理员".equals(roleName) || "管理员组".equals(roleName);
}
```

**数据查询权限：**
- 管理员角色：可以查看所有经办人的项目
- 普通用户：只能查看自己作为经办人的项目

**统计数据计算：**
- 从数据库实时查询项目统计数据
- 支持按状态、经办人、部门等维度统计
- 金额统计自动转换为万元单位

## 前端实现

### 1. 管理端首页更新

#### manager_home.vue 主要更改：

**统计卡片显示：**
- 显示公司和部门两个维度的统计数据
- 未开始项目、已完成项目分别统计
- 智能帮助使用次数统计
- 已完成项目金额统计

**项目列表表格：**
- 新增"经办人"列，显示经办人姓名
- 更新"状态"列，使用不同颜色的标签显示状态
- 状态颜色映射：
  - 未开始：info（灰色）
  - 询价中：warning（橙色）
  - 已完成：success（绿色）
  - 已终止：danger（红色）

## 权限控制机制

### 1. 角色判断
```java
// 管理员角色
"超级管理员".equals(roleName) || "管理员组".equals(roleName)
```

### 2. 数据权限
- **管理员组/超级管理员**：
  - 可以查看所有项目
  - 统计数据包含全公司数据
  - 部门统计按当前用户部门过滤

- **普通用户**：
  - 只能查看自己作为经办人的项目
  - 统计数据只包含自己的项目
  - 部门统计等同于个人统计

### 3. 查询条件
```java
// 非管理员只能查看自己的项目
if (!isAdmin) {
    queryWrapper.eq("handler_id", currentUser.getUserId());
}
```

## API接口

### 1. 获取首页统计数据
```
GET /api/xjzs/home/<USER>
```

**返回数据结构：**
```json
{
  "success": true,
  "data": {
    "notStartedProjects": {
      "companyCount": 5,
      "departmentCount": 2
    },
    "completedProjects": {
      "companyCount": 3,
      "departmentCount": 1
    },
    "intelligentHelp": {
      "companyUsageCount": 15000,
      "departmentUsageCount": 5600
    },
    "completedProjectAmount": {
      "companyAmount": "¥26.45万",
      "departmentAmount": "¥6.45万"
    }
  }
}
```

### 2. 获取最近项目列表
```
GET /api/xjzs/home/<USER>
```

**返回数据结构：**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "name": "2025年办公设备采购项目",
        "projectNumber": "XW-2025-0001",
        "dept": "技术部",
        "budget": 1500000,
        "type": "设备类",
        "status": 0,
        "statusName": "未开始",
        "handlerId": 100,
        "handlerName": "张三",
        "createTime": "2025-01-19T10:00:00",
        "updateTime": "2025-01-19T10:00:00"
      }
    ],
    "total": 10,
    "current": 1,
    "size": 10
  }
}
```

## 测试验证

### 1. 数据库测试
```sql
-- 验证字段添加
DESCRIBE xjzs_project;

-- 验证索引创建
SHOW INDEX FROM xjzs_project;

-- 验证测试数据
SELECT * FROM xjzs_project WHERE handler_id IS NOT NULL;
```

### 2. 功能测试

#### 管理员用户测试：
1. 使用管理员账号登录
2. 访问管理端首页 `/xjzs/manager_home`
3. 验证可以看到所有项目
4. 验证统计数据包含全公司数据

#### 普通用户测试：
1. 使用普通用户账号登录
2. 访问用户端首页 `/xjzs/user_home`
3. 验证只能看到自己的项目
4. 验证统计数据只包含个人数据

### 3. 权限测试
- 测试不同角色用户的数据访问权限
- 验证经办人字段正确显示
- 验证项目状态正确显示和颜色标识

## 部署说明

### 1. 数据库更新
```bash
# 执行数据库更新脚本
mysql -u username -p database_name < src/main/resources/sql/update_xjzs_project.sql

# 可选：插入测试数据
mysql -u username -p database_name < src/main/resources/sql/test_data.sql
```

### 2. 应用重启
```bash
# 重新编译并启动后端服务
mvn clean package
java -jar target/zjyc-zgxjbszs-backend.jar

# 重新构建并启动前端服务
npm run build
npm run serve
```

## 注意事项

1. **数据迁移**：现有项目数据的status字段默认为0（未开始），handler_id为NULL
2. **权限控制**：确保用户角色名称准确设置为"超级管理员"或"管理员组"
3. **性能优化**：已添加必要的数据库索引，大数据量时建议进一步优化
4. **数据一致性**：建议在生产环境中添加外键约束确保数据一致性

## 扩展建议

1. **状态流转**：可以添加项目状态流转的业务逻辑和权限控制
2. **通知机制**：项目状态变更时发送通知给相关人员
3. **审批流程**：集成工作流引擎实现项目审批流程
4. **数据导出**：支持项目数据的Excel导出功能
