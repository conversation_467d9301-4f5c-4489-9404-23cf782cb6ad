/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.common.cache.UserCache;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.xjzs.pojo.entity.ProjectEntity;
import org.springblade.modules.xjzs.service.IProjectService;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.xjzs.pojo.entity.ProjectInquiryRecordEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectInquiryRecordVO;
import org.springblade.modules.xjzs.excel.ProjectInquiryRecordExcel;
import org.springblade.modules.xjzs.wrapper.ProjectInquiryRecordWrapper;
import org.springblade.modules.xjzs.service.IProjectInquiryRecordService;
import org.springblade.modules.xjzs.util.WordTemplateUtil;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;
import org.springframework.web.multipart.MultipartFile;

import java.text.DecimalFormat;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * 项目询价记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/projectInquiryRecord")
@Tag(name = "项目询价记录表", description = "项目询价记录表接口")
@Slf4j
public class ProjectInquiryRecordController extends BladeController {

	private final IProjectInquiryRecordService projectInquiryRecordService;

	private final IProjectService projectService;

	/**
	 * 项目询价记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入projectInquiryRecord")
	public R<ProjectInquiryRecordVO> detail(ProjectInquiryRecordEntity projectInquiryRecord) {
		ProjectInquiryRecordEntity detail = projectInquiryRecordService.getOne(Condition.getQueryWrapper(projectInquiryRecord));
		ProjectInquiryRecordVO vo = ProjectInquiryRecordWrapper.build().entityVO(detail);

		// 获取项目信息
		if (detail != null && detail.getProjectId() != null) {
			ProjectEntity project = projectService.getById(detail.getProjectId());
			if (project != null) {
				vo.setProjectName(project.getName());
//				vo.setProjectType(project.getType());
//				vo.setProjectBudget(project.getBudget());
			}
		}

		return R.data(vo);
	}
	/**
	 * 项目询价记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入projectInquiryRecord")
	public R<IPage<ProjectInquiryRecordVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> projectInquiryRecord, Query query) {
		IPage<ProjectInquiryRecordEntity> pages = projectInquiryRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(projectInquiryRecord, ProjectInquiryRecordEntity.class));
		return R.data(ProjectInquiryRecordWrapper.build().pageVO(pages));
	}

	/**
	 * 项目询价记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入projectInquiryRecord")
	public R<IPage<ProjectInquiryRecordVO>> page(ProjectInquiryRecordVO projectInquiryRecord, Query query) {
		IPage<ProjectInquiryRecordVO> pages = projectInquiryRecordService.selectProjectInquiryRecordPage(Condition.getPage(query), projectInquiryRecord);
		return R.data(pages);
	}

	/**
	 * 项目询价记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入projectInquiryRecord")
	public R save(@Valid @RequestBody ProjectInquiryRecordEntity projectInquiryRecord) {
		return R.status(projectInquiryRecordService.save(projectInquiryRecord));
	}

	/**
	 * 项目询价记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入projectInquiryRecord")
	public R update(@Valid @RequestBody ProjectInquiryRecordEntity projectInquiryRecord) {
		return R.status(projectInquiryRecordService.updateById(projectInquiryRecord));
	}

	/**
	 * 项目询价记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入projectInquiryRecord")
	public R submit(@Valid @RequestBody ProjectInquiryRecordEntity projectInquiryRecord) {
		return R.status(projectInquiryRecordService.saveProjectInquiryRecord(projectInquiryRecord));
	}

	/**
	 * 项目询价记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(projectInquiryRecordService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-projectInquiryRecord")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入projectInquiryRecord")
	public void exportProjectInquiryRecord(@Parameter(hidden = true) @RequestParam Map<String, Object> projectInquiryRecord, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ProjectInquiryRecordEntity> queryWrapper = Condition.getQueryWrapper(projectInquiryRecord, ProjectInquiryRecordEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ProjectInquiryRecord::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(ProjectInquiryRecordEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ProjectInquiryRecordExcel> list = projectInquiryRecordService.exportProjectInquiryRecord(queryWrapper);
		ExcelUtil.export(response, "项目询价记录表数据" + DateUtil.time(), "项目询价记录表数据表", list, ProjectInquiryRecordExcel.class);
	}

	/**
	 * 导出询价函
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "导出询价函", description = "传入询价记录ID")
	public ResponseEntity<byte[]> exportInquiry(@RequestParam Long id) {
		XWPFDocument document = null;
		try {
			// 获取询价记录详情
			ProjectInquiryRecordEntity record = projectInquiryRecordService.getById(id);
			if (record == null) {
				return ResponseEntity.notFound().build();
			}
			ProjectEntity project = projectService.getById(record.getProjectId());
			if (project == null) {
				return ResponseEntity.notFound().build();
			}

			// 加载Word模板
			try {
				if(record.getProjectType().equals("服务类")){
					document = WordTemplateUtil.loadTemplate("doctemplete/inquiry_service_template.docx");
				}else{
					document = WordTemplateUtil.loadTemplate("doctemplete/inquiry_goods_template.docx");
				}
			} catch (IOException e) {
				log.error("加载询价函模板失败", e);
				// 如果模板加载失败，使用代码生成Word文档
				document = createInquiryDocumentProgrammatically(record, project);
			}

			// 准备替换的占位符数据
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
			String currentDate = sdf.format(new Date());

			Map<String, String> placeholders = new HashMap<>();
			placeholders.put("projectName", project.getName());
			placeholders.put("projectBudget", record.getProjectBudget() + "");
			placeholders.put("procurementMethod", record.getProcurementMethod());
			placeholders.put("deadline", sdf.format(record.getDeadline()));
			if(record.getProjectType().equals("服务类")){
				//服务
				placeholders.put("serviceContent", record.getServiceContent());
				placeholders.put("serviceRequirements", record.getServiceRequirements());
				placeholders.put("servicePeriod", record.getServicePeriod());
			}else{
				placeholders.put("specifications", record.getSpecifications());
				placeholders.put("quantityStandard", record.getQuantityStandard());
				placeholders.put("quantity", record.getQuantity() != null ? record.getQuantity().toString() : "");
			}
			placeholders.put("productName", record.getProductName());
			placeholders.put("contactPhone", record.getContactPhone());
			placeholders.put("contactPerson", record.getContactName());
			User user = UserCache.getUser(record.getCreateUser());
			placeholders.put("purchaser", user.getRealName());
			placeholders.put("currentDate", currentDate);

			// 替换模板中的占位符
			WordTemplateUtil.replacePlaceholders(document, placeholders);

			// 将文档写入字节数组
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			document.write(outputStream);
			byte[] documentBytes = outputStream.toByteArray();

			// 设置响应头
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"));
			headers.setContentDispositionFormData("attachment",
				java.net.URLEncoder.encode("询价函_" + project.getName() + ".docx", "UTF-8"));

			return ResponseEntity.ok()
				.headers(headers)
				.body(documentBytes);
		} catch (Exception e) {
			log.error("导出询价函失败", e);
			return ResponseEntity.status(500).build();
		} finally {
			// 确保关闭 XWPFDocument 以释放资源
			if (document != null) {
				try {
					document.close();
				} catch (IOException e) {
					log.error("关闭Word文档时发生错误", e);
				}
			}
		}
	}

	/**
	 * 导出回函
	 */
	@GetMapping("/exportReply")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "导出回函", description = "传入询价记录ID")
	public ResponseEntity<byte[]> exportInquiryReply(@RequestParam Long id) {
		XWPFDocument document = null;
		try {
			// 获取询价记录详情
			ProjectInquiryRecordEntity record = projectInquiryRecordService.getById(id);
			if (record == null) {
				return ResponseEntity.notFound().build();
			}
			ProjectEntity project = projectService.getById(record.getProjectId());
			if (project == null) {
				return ResponseEntity.notFound().build();
			}

			// 加载Word模板
			try {
				document = WordTemplateUtil.loadTemplate("doctemplete/inquiry_reply_template.docx");
			} catch (IOException e) {
				log.error("加载回函模板失败", e);
				// 如果模板加载失败，使用代码生成Word文档
				document = createInquiryDocumentProgrammatically(record, project);
			}

			Map<String, String> placeholders = new HashMap<>();
			placeholders.put("projectName", project.getName());

			// 替换模板中的占位符
			WordTemplateUtil.replacePlaceholders(document, placeholders);

			// 将文档写入字节数组
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			document.write(outputStream);
			byte[] documentBytes = outputStream.toByteArray();

			// 设置响应头
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"));
			headers.setContentDispositionFormData("attachment",
					java.net.URLEncoder.encode("回函_" + project.getName() + ".docx", "UTF-8"));

			return ResponseEntity.ok()
					.headers(headers)
					.body(documentBytes);
		} catch (Exception e) {
			log.error("导出回函失败", e);
			return ResponseEntity.status(500).build();
		} finally {
			// 确保关闭 XWPFDocument 以释放资源
			if (document != null) {
				try {
					document.close();
				} catch (IOException e) {
					log.error("关闭Word文档时发生错误", e);
				}
			}
		}
	}

	/**
	 * 以编程方式创建询价函文档（当模板不可用时使用）
	 */
	private XWPFDocument createInquiryDocumentProgrammatically(ProjectInquiryRecordEntity record, ProjectEntity project) {
		XWPFDocument document = new XWPFDocument();

		// 设置标题
		XWPFParagraph titleParagraph = document.createParagraph();
		titleParagraph.setAlignment(ParagraphAlignment.CENTER);
		XWPFRun titleRun = titleParagraph.createRun();
		titleRun.setText("询价函");
		titleRun.setBold(true);
		titleRun.setFontSize(20);
		titleRun.setFontFamily("宋体");

		// 添加日期
		XWPFParagraph dateParagraph = document.createParagraph();
		dateParagraph.setAlignment(ParagraphAlignment.RIGHT);
		XWPFRun dateRun = dateParagraph.createRun();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
		dateRun.setText("日期：" + sdf.format(new Date()));
		dateRun.setFontSize(12);
		dateRun.setFontFamily("宋体");

		// 添加正文
		XWPFParagraph contentParagraph = document.createParagraph();
		contentParagraph.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun contentRun = contentParagraph.createRun();
		contentRun.setText("尊敬的供应商：");
		contentRun.setFontSize(12);
		contentRun.setFontFamily("宋体");
		contentRun.addBreak();
		contentRun.addBreak();
		contentRun.setText("    我公司正在进行" + project.getName() + "项目的采购工作，诚邀贵公司参与报价。具体询价内容如下：");
		contentRun.addBreak();
		contentRun.addBreak();

		// 创建表格
		XWPFTable table = document.createTable(9, 2);
		table.setWidth("100%");

		// 设置表格样式
		CTTblPr tblPr = table.getCTTbl().getTblPr();
		CTTblWidth tblWidth = tblPr.isSetTblW() ? tblPr.getTblW() : tblPr.addNewTblW();

		tblWidth.setType(STTblWidth.PCT);

		// 填充表格内容
		setCellText(table, 0, 0, "项目名称");
		setCellText(table, 0, 1, project.getName());

		setCellText(table, 1, 0, "产品名称/型号");
		setCellText(table, 1, 1, record.getProductName());

		setCellText(table, 2, 0, "技术规格");
		setCellText(table, 2, 1, record.getSpecifications());

		setCellText(table, 3, 0, "数量");
		setCellText(table, 3, 1, record.getQuantity() != null ? record.getQuantity().toString() : "");

		setCellText(table, 4, 0, "质量标准");
		setCellText(table, 4, 1, record.getQuantityStandard());

		setCellText(table, 7, 0, "服务要求");
		setCellText(table, 7, 1, record.getServiceRequirements());

		// 添加报价要求
		XWPFParagraph requirementParagraph = document.createParagraph();
		requirementParagraph.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun requirementRun = requirementParagraph.createRun();
		requirementRun.addBreak();
		requirementRun.setText("报价要求：");
		requirementRun.setFontSize(12);
		requirementRun.setFontFamily("宋体");
		requirementRun.setBold(true);
		requirementRun.addBreak();

		XWPFParagraph requirement1 = document.createParagraph();
		requirement1.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun requirement1Run = requirement1.createRun();
		requirement1Run.setText("1. 请在报价单中详细列明产品的技术参数、单价、总价等信息。");
		requirement1Run.setFontSize(12);
		requirement1Run.setFontFamily("宋体");

		XWPFParagraph requirement2 = document.createParagraph();
		requirement2.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun requirement2Run = requirement2.createRun();
		requirement2Run.setText("2. 报价截止时间：" + record.getDeadline());
		requirement2Run.setFontSize(12);
		requirement2Run.setFontFamily("宋体");

		XWPFParagraph requirement3 = document.createParagraph();
		requirement3.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun requirement3Run = requirement3.createRun();
		requirement3Run.setText("3. 请将报价单发送至我公司采购部门。");
		requirement3Run.setFontSize(12);
		requirement3Run.setFontFamily("宋体");

		// 添加联系方式
		XWPFParagraph contactParagraph = document.createParagraph();
		contactParagraph.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun contactRun = contactParagraph.createRun();
		contactRun.addBreak();
		contactRun.setText("联系方式：");
		contactRun.setFontSize(12);
		contactRun.setFontFamily("宋体");
		contactRun.setBold(true);
		contactRun.addBreak();

		XWPFParagraph contact1 = document.createParagraph();
		contact1.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun contact1Run = contact1.createRun();
		contact1Run.setText("联系人：采购部");
		contact1Run.setFontSize(12);
		contact1Run.setFontFamily("宋体");

		XWPFParagraph contact2 = document.createParagraph();
		contact2.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun contact2Run = contact2.createRun();
		contact2Run.setText("电话：0759-XXXXXXXX");
		contact2Run.setFontSize(12);
		contact2Run.setFontFamily("宋体");

		XWPFParagraph contact3 = document.createParagraph();
		contact3.setAlignment(ParagraphAlignment.LEFT);
		XWPFRun contact3Run = contact3.createRun();
		contact3Run.setText("邮箱：<EMAIL>");
		contact3Run.setFontSize(12);
		contact3Run.setFontFamily("宋体");

		// 添加落款
		XWPFParagraph signatureParagraph = document.createParagraph();
		signatureParagraph.setAlignment(ParagraphAlignment.RIGHT);
		XWPFRun signatureRun = signatureParagraph.createRun();
		signatureRun.addBreak();
		signatureRun.addBreak();
		signatureRun.setText("湛江有限公司");
		signatureRun.setFontSize(12);
		signatureRun.setFontFamily("宋体");
		signatureRun.addBreak();
		signatureRun.setText(sdf.format(new Date()));

		return document;
	}

	/**
	 * 设置单元格文本
	 */
	private void setCellText(XWPFTable table, int row, int col, String text) {
		XWPFTableCell cell = table.getRow(row).getCell(col);
		cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

		XWPFParagraph paragraph = cell.getParagraphs().get(0);
		paragraph.setAlignment(ParagraphAlignment.LEFT);

		XWPFRun run = paragraph.createRun();
		run.setText(text != null ? text : "");
		run.setFontSize(12);
		run.setFontFamily("宋体");

		// 设置第一列为粗体
		if (col == 0) {
			run.setBold(true);
		}
	}

	/**
	 * 项目询价记录 分页
	 */
	@GetMapping("/project-inquiry-list")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "项目询价记录分页", description = "传入查询参数")
	public R<Object> projectInquiryList(@RequestParam(required = false) String projectName,
								@RequestParam(defaultValue = "1") Integer current,
								@RequestParam(defaultValue = "10") Integer size) {
		return R.data(projectInquiryRecordService.getProjectInquiryList(projectName, current, size));
	}

	/**
	 * 获取供应商列表
	 */
	@GetMapping("/supplier-list")
	@ApiOperationSupport(order = 12)
	@Operation(summary = "获取供应商列表", description = "传入询价ID")
	public R<Object> getSupplierList(@RequestParam Long inquiryId) {
		// 这里应该实现获取供应商列表的逻辑
		// 暂时返回空数据
		return R.data(null);
	}

	/**
	 * 上传报价回函
	 */
	@PostMapping("/upload-quotation")
	@ApiOperationSupport(order = 13)
	@Operation(summary = "上传报价回函", description = "传入文件和询价ID")
	public R<Object> uploadQuotation(@RequestParam("file") MultipartFile file, @RequestParam("inquiryId") Long inquiryId) {
		try {
			boolean success = projectInquiryRecordService.processQuotationDocument(file, inquiryId);
			if (success) {
				return R.success("报价回函上传成功");
			} else {
				return R.fail("报价回函处理失败");
			}
		} catch (Exception e) {
			log.error("上传报价回函失败", e);
			return R.fail("上传失败：" + e.getMessage());
		}
	}

}
