# 项目询价状态功能实现说明

## 功能概述

在 `projectAssistantWaterfall.vue` 的项目选择步骤中，当选择的项目状态为 1（询价中）时，标准表区域会显示为采购询价记录表，该表格为只读模式，展示该项目的所有采购询价记录。

## 实现细节

### 1. 数据结构修改

#### projectAssistantWaterfall.vue
- 在 `formData` 中添加了 `projectStatus` 字段用于存储项目状态
- 在项目详情加载时设置项目状态：`formData.projectStatus = projectData.projectStatus || 0`

#### ProjectSelectStep.vue
- 添加了询价状态相关的响应式数据：
  - `isInquiryStatus`: 标识是否为询价状态
  - `inquiryRecordData`: 存储采购询价记录数据
  - `inquiryRecordLoading`: 加载状态

### 2. API 集成

- 引入了 `getQueryPage` API 从 `@/api/xjzs/supplierInquiryRecord`
- 实现了 `loadInquiryRecordData` 方法根据项目ID查询采购询价记录

### 3. 界面实现

#### 条件渲染
```vue
<!-- 当项目状态为1（询价中）时显示采购询价记录表 -->
<div v-if="isInquiryStatus" class="inquiry-record-table">
  <!-- 采购询价记录表内容 -->
</div>
<!-- 根据算法类型动态显示不同的标准表 -->
<div v-else class="table-selector">
  <!-- 原有的标准表内容 -->
</div>
```

#### 表格字段
采购询价记录表包含以下字段：
- 项目名称
- 预算金额（万元）
- 询价部门
- 经办人
- 询价时间
- 名称
- 技术参数或服务内容
- 数量
- 询价对象
- 询价渠道
- 单价（万元）
- 总价（万元）
- 备注

### 4. 状态检查逻辑

#### 项目选择时检查
```javascript
// 检查项目状态是否为1（询价中）
if (project.projectStatus === 1) {
  isInquiryStatus.value = true;
  // 加载采购询价记录数据
  loadInquiryRecordData(project.id);
} else {
  isInquiryStatus.value = false;
  inquiryRecordData.value = [];
}
```

#### 详情模式检查
添加了对 `formData` 的深度监听，确保在详情模式下也能正确检查项目状态。

### 5. 验证逻辑优化

在 `nextStep` 方法中添加了特殊处理：
```javascript
// 如果项目状态为询价中，直接跳过标准表验证
if (props.formData.projectStatus === 1) {
  console.log('项目状态为询价中，跳过标准表验证');
  emit('next-step');
  return;
}
```

### 6. 数据格式化

- `formatDate`: 格式化日期显示
- `formatPrice`: 将价格转换为万元显示（除以10000并保留2位小数）

### 7. 样式优化

添加了专门的样式类：
- `.inquiry-record-table`: 询价记录表容器
- `.inquiry-table-wrapper`: 表格包装器，包含圆角和阴影效果
- `.empty-data`: 空数据状态显示

## 使用场景

1. **新建项目流程**: 项目状态为0（未开始）时，显示正常的标准表填写界面
2. **询价中项目**: 项目状态为1（询价中）时，显示只读的采购询价记录表
3. **详情查看**: 在详情模式下，根据项目状态自动切换显示内容

## 技术特点

- **响应式设计**: 使用 Vue 3 的响应式系统，状态变化时自动更新界面
- **条件渲染**: 根据项目状态动态切换显示内容
- **数据懒加载**: 只有在需要时才加载采购询价记录数据
- **错误处理**: 包含完整的错误处理和用户提示
- **样式一致性**: 保持与原有界面风格的一致性

## 项目状态说明

根据 `ProjectStatusEnum.java`：
- 0: 未开始
- 1: 询价中 ⭐ (本功能针对此状态)
- 2: 已完成
- 9: 已终止
